"""
Queue display component for the Tire Panorama Tool.

Displays the queue of panorama generation processes in the sidebar.
"""
import tkinter as tk
from tkinter import ttk, messagebox
import time
from typing import List, Dict, Any, Optional


class QueueDisplayComponent:
    """
    Component for displaying the queue of panorama generation processes.

    This component creates a UI panel that shows the current queue state,
    including running and queued processes, along with their progress.
    """

    def __init__(self, parent, app):
        """
        Initialize the queue display component.

        Args:
            parent: Parent widget
            app: Main application instance
        """
        self.parent = parent
        self.app = app

        # Create the main frame
        self.frame = tk.Frame(parent, bg="#ffa500")

        # Create the header
        self.header_frame = tk.Frame(self.frame, bg="#ffa500", pady=5)
        self.header_frame.pack(fill=tk.X)

        self.header_label = tk.Label(
            self.header_frame,
            text="Process Queue",
            bg="#ffa500",
            fg="white",
            font=("Ubuntu", 10, "bold")
        )
        self.header_label.pack(side=tk.LEFT, padx=10)

        # Create clear button for completed tasks
        self.clear_btn = tk.Button(
            self.header_frame,
            text="Clear",
            bg="#e69400",
            fg="white",
            font=("Ubuntu", 8),
            bd=0,
            padx=5,
            pady=2,
            command=self._on_clear_completed
        )
        self.clear_btn.pack(side=tk.RIGHT, padx=10)

        # Create the contents area
        self.contents_frame = tk.Frame(self.frame, bg="#ffa500")
        self.contents_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create the empty message
        self.empty_label = tk.Label(
            self.contents_frame,
            text="No active processes",
            bg="#ffa500",
            fg="white",
            font=("Ubuntu", 9)
        )
        self.empty_label.pack(pady=10)

        # List to store process panels
        self.process_panels = []

        # Dictionary to track task panels by ID for more stable updates
        self.task_panels = {}

        # Add a flag to prevent concurrent updates
        self.is_updating = False

        # Add a timer to throttle updates
        self.last_update_time = 0

        # Add a scheduled update ID to prevent multiple pending updates
        self.scheduled_update_id = None

        # Register for queue status changes
        if hasattr(app, 'process_queue_manager'):
            app.process_queue_manager.register_status_changed_callback(
                self._safe_update_display
            )

    def pack(self, **kwargs):
        """Pack the frame with the given options."""
        self.frame.pack(**kwargs)

    def grid(self, **kwargs):
        """Grid the frame with the given options."""
        self.frame.grid(**kwargs)

    def _safe_update_display(self):
        """Safely update the display by routing through the UI thread."""
        # Queue the update in the UI thread to avoid cross-thread UI operations
        try:
            # Check if we already have a scheduled update
            if self.scheduled_update_id is not None:
                return  # Skip this update request as one is already pending

            # Check if it's too soon for another update
            current_time = time.time()
            if current_time - self.last_update_time < 0.5:  # Limit to one update every 500ms
                return

            if hasattr(self.app, 'root') and self.app.root:
                # Schedule the update with a delay to throttle refreshes
                self.scheduled_update_id = self.app.root.after(500, self._execute_update)
                self.last_update_time = current_time
        except Exception as e:
            print(f"Error in safe update display: {e}")

    def _execute_update(self):
        """Execute the actual update after the delay"""
        try:
            # Reset the scheduled update ID
            self.scheduled_update_id = None

            # Only proceed if we're not already updating
            if self.is_updating:
                return

            self.is_updating = True
            self.update_display()
        finally:
            self.is_updating = False

    def update_display(self):
        """
        Update the display with the current queue state.

        This should be called whenever the queue status changes.
        """
        try:
            # Get active tasks
            active_tasks = []
            if hasattr(self.app, 'process_queue_manager'):
                active_tasks = self.app.process_queue_manager.get_active_tasks()

            # Create a dictionary of tasks by ID for easier lookup
            active_tasks_by_id = {task['id']: task for task in active_tasks}

            # Get all task IDs (current and new)
            current_task_ids = set(self.task_panels.keys())
            new_task_ids = set(active_tasks_by_id.keys())

            # Determine which tasks to add, update, or remove
            tasks_to_add = new_task_ids - current_task_ids
            tasks_to_remove = current_task_ids - new_task_ids
            tasks_to_update = current_task_ids.intersection(new_task_ids)

            # If the task structure has changed, we need a full rebuild
            needs_rebuild = len(tasks_to_add) > 0 or len(tasks_to_remove) > 0

            # Update existing panels without rebuilding
            if not needs_rebuild:
                # Just update progress for existing panels
                for task_id in tasks_to_update:
                    panel = self.task_panels.get(task_id)
                    task = active_tasks_by_id.get(task_id)

                    if panel and task and hasattr(panel, 'progress_var') and hasattr(panel, 'percent_label'):
                        # Get task status
                        status = task['status']
                        status_str = status.name if hasattr(status, 'name') else str(status)

                        # Only update running tasks
                        if status_str == 'RUNNING':
                            # Update progress bar and label only if the value has changed significantly
                            percent = int(task['progress'])
                            current_percent = int(panel.progress_var.get())
                            if abs(percent - current_percent) >= 1:  # Reduced threshold for more responsive updates
                                panel.progress_var.set(percent)

                                # Determine phase based on progress (0-50% = Phase 1, 50-100% = Phase 2)
                                if percent <= 50:
                                    phase_text = f"{percent}% (1/2)"
                                else:
                                    phase_text = f"{percent}% (2/2)"

                                panel.percent_label.config(text=phase_text)

                # Update clear button state
                self._update_clear_button()
                return  # No need for full rebuild

            # We need to do a full rebuild
            # Clear existing panels
            for panel in self.process_panels:
                panel.destroy()
            self.process_panels = []
            self.task_panels = {}

            # Update clear button state
            self._update_clear_button()

            # Show empty message if no active tasks
            if not active_tasks:
                self.empty_label.pack(pady=10)
            else:
                self.empty_label.pack_forget()

                # Create panels for each task (limited to 5)
                display_count = min(5, len(active_tasks))

                # Convert enum to string for comparison if needed
                for task in active_tasks:
                    status = task["status"]
                    if hasattr(status, "name"):  # It's an enum
                        task["status_str"] = status.name
                    else:
                        task["status_str"] = str(status)

                # Sort by status (running first) then by added time
                active_tasks.sort(key=lambda t: (
                    0 if t["status_str"] == "RUNNING" else 1,
                    t["added_time"]
                ))

                for i in range(display_count):
                    task = active_tasks[i]
                    panel = self._create_process_panel(task)
                    panel.pack(fill=tk.X, pady=2)
                    self.process_panels.append(panel)

                    # Store panel in task_panels dictionary for easier updates
                    if hasattr(panel, 'task_id'):
                        self.task_panels[panel.task_id] = panel

                # Add "+X more" if there are more tasks
                if len(active_tasks) > 5:
                    more_count = len(active_tasks) - 5
                    more_label = tk.Label(
                        self.contents_frame,
                        text=f"+ {more_count} more in queue...",
                        bg="#ffa500",
                        fg="white",
                        font=("Ubuntu", 9, "italic")
                    )
                    more_label.pack(fill=tk.X, pady=5)
                    self.process_panels.append(more_label)
        except Exception as e:
            print(f"Error updating queue display: {e}")
            # Create a simple error message
            error_label = tk.Label(
                self.contents_frame,
                text="Error updating queue display",
                bg="#ffa500",
                fg="white",
                font=("Ubuntu", 9)
            )
            error_label.pack(pady=10)
            self.process_panels.append(error_label)

    def _create_process_panel(self, task: Dict[str, Any]) -> tk.Frame:
        """
        Create a panel for a specific process with improved layout.

        Args:
            task: Task dictionary

        Returns:
            Panel frame
        """
        try:
            # Create panel with more height
            panel = tk.Frame(self.contents_frame, bg="#ffb733", bd=1, relief=tk.SOLID, pady=8)

            # Project name and subproject as a header
            header_frame = tk.Frame(panel, bg="#ffb733")
            header_frame.pack(fill=tk.X, padx=5, pady=(5, 2))

            # Project name
            project_name = task["project_info"].get("name", "Unknown Project")
            subproject_type = task["subproject_type"].capitalize()

            header_text = f"{project_name}"
            header_label = tk.Label(
                header_frame,
                text=header_text,
                bg="#ffb733",
                fg="black",
                font=("Ubuntu", 10, "bold"),
                anchor=tk.W
            )
            header_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

            # Subproject as a subtitle
            subtitle_frame = tk.Frame(panel, bg="#ffb733")
            subtitle_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

            subtitle_label = tk.Label(
                subtitle_frame,
                text=f"{subproject_type} Panorama",
                bg="#ffb733",
                fg="#333333",
                font=("Ubuntu", 9),
                anchor=tk.W
            )
            subtitle_label.pack(side=tk.LEFT)

            # Status with color coding - in its own row
            status_frame = tk.Frame(panel, bg="#ffb733")
            status_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

            status = task["status"]
            # Convert to string if it's an enum
            if hasattr(status, "name"):
                status_text = status.name.capitalize()
            else:
                status_text = str(status).capitalize()

            # Make the status text more user-friendly
            status_text = {
                "QUEUED": "Queued",
                "RUNNING": "Running",
                "COMPLETED": "Completed",
                "FAILED": "Failed",
                "CANCELLED": "Cancelled"
            }.get(status_text, status_text)

            # Set status color based on status
            status_colors = {
                "QUEUED": "#f0ad4e",    # Orange
                "RUNNING": "#5bc0de",   # Blue
                "COMPLETED": "#5cb85c", # Green
                "FAILED": "#d9534f",    # Red
                "CANCELLED": "#d9534f"  # Red
            }

            # Get status string for comparison
            status_str = status.name if hasattr(status, "name") else str(status)
            status_color = status_colors.get(status_str, "#777777")

            status_indicator = tk.Frame(status_frame, width=12, height=12, bg=status_color)
            status_indicator.pack(side=tk.LEFT, padx=(0, 5))

            status_label = tk.Label(
                status_frame,
                text=f"Status: {status_text}",
                bg="#ffb733",
                fg="black",
                font=("Ubuntu", 9),
                anchor=tk.W
            )
            status_label.pack(side=tk.LEFT)

            # Time information
            time_frame = tk.Frame(panel, bg="#ffb733")
            time_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

            # Format added time nicely
            import time
            from datetime import datetime

            added_time = task.get("added_time", 0)
            if added_time:
                # Convert to readable format
                time_str = datetime.fromtimestamp(added_time).strftime("%H:%M:%S")
                time_label = tk.Label(
                    time_frame,
                    text=f"Added: {time_str}",
                    bg="#ffb733",
                    fg="#555555",
                    font=("Ubuntu", 8),
                    anchor=tk.W
                )
                time_label.pack(side=tk.LEFT)

            # Add continuation info if available
            if task["continue_from_state"]:
                info_frame = tk.Frame(panel, bg="#ffb733")
                info_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

                stage = task["continue_from_state"].get("processing_stage", "")
                frame = task["continue_from_state"].get("last_processed_frame", 0)

                stage_text = "frame extraction" if stage == "frame_extraction" else "panorama stitching"
                if frame > 0:
                    info_text = f"Continuing from {stage_text} at frame {frame}"
                else:
                    info_text = f"Continuing from {stage_text}"

                info_label = tk.Label(
                    info_frame,
                    text=info_text,
                    bg="#ffb733",
                    fg="#333333",
                    font=("Ubuntu", 8, "italic"),
                    anchor=tk.W
                )
                info_label.pack(fill=tk.X)

            # Progress bar and percentage (only for running tasks)
            # Convert to string if it's an enum for comparison
            status_str = status.name if hasattr(status, "name") else str(status)

            if status_str == "RUNNING":
                progress_frame = tk.Frame(panel, bg="#ffb733")
                progress_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

                progress_var = tk.DoubleVar(value=task["progress"])

                # Text showing percentage with phase indicator
                percent = int(task["progress"])

                # Determine phase based on progress (0-50% = Phase 1, 50-100% = Phase 2)
                if percent <= 50:
                    phase_text = f"{percent}% (1/2)"
                else:
                    phase_text = f"{percent}% (2/2)"

                percent_label = tk.Label(
                    progress_frame,
                    text=phase_text,
                    bg="#ffb733",
                    fg="black",
                    font=("Ubuntu", 8),
                    width=8,  # Increased width to accommodate phase indicator
                    anchor=tk.W
                )
                percent_label.pack(side=tk.LEFT)

                progress_bar = ttk.Progressbar(
                    progress_frame,
                    variable=progress_var,
                    mode='determinate',
                    length=100
                )
                progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)

                # Add task progress to the panel for updates
                panel.progress_var = progress_var
                panel.percent_label = percent_label
                panel.task_id = task["id"]

                # Add cancel button for running tasks
                cancel_frame = tk.Frame(panel, bg="#ffb733")
                cancel_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

                cancel_btn = tk.Button(
                    cancel_frame,
                    text="Cancel",
                    bg="#e69400",
                    fg="white",
                    font=("Ubuntu", 8),
                    bd=0,
                    padx=3,
                    pady=1,
                    command=lambda tid=task["id"]: self._on_cancel_task(tid)
                )
                cancel_btn.pack(side=tk.RIGHT)

            # For queued tasks, add a cancel button
            elif status_str == "QUEUED":
                cancel_frame = tk.Frame(panel, bg="#ffb733")
                cancel_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

                cancel_btn = tk.Button(
                    cancel_frame,
                    text="Remove",
                    bg="#e69400",
                    fg="white",
                    font=("Ubuntu", 8),
                    bd=0,
                    padx=3,
                    pady=1,
                    command=lambda tid=task["id"]: self._on_cancel_task(tid)
                )
                cancel_btn.pack(side=tk.RIGHT)

            # Add a separator line at the bottom
            ttk.Separator(panel, orient=tk.HORIZONTAL).pack(fill=tk.X, padx=5, pady=(5, 0))

            return panel
        except Exception as e:
            print(f"Error creating process panel: {e}")
            # Create a simple error panel
            error_panel = tk.Frame(self.contents_frame, bg="#ffb733", bd=1, relief=tk.SOLID)
            tk.Label(
                error_panel,
                text="Error showing task",
                bg="#ffb733",
                fg="black",
                font=("Ubuntu", 9),
                anchor=tk.W
            ).pack(padx=5, pady=5)
            return error_panel

    def _on_cancel_task(self, task_id):
        """Handle click on the cancel task button."""
        try:
            # Use the app's centralized cancel_task method to ensure consistency
            if hasattr(self.app, 'cancel_queue_task'):
                success = self.app.cancel_queue_task(task_id)
                if not success:
                    messagebox.showwarning("Warning", "Unable to cancel task. It may already be running or completed.")
            else:
                messagebox.showwarning("Warning", "Cancel functionality not available.")
        except Exception as e:
            print(f"Error cancelling task: {e}")
            messagebox.showerror("Error", f"Error cancelling task: {str(e)}")

    def _update_clear_button(self):
        """Update the clear button state based on completed tasks"""
        try:
            if hasattr(self.app, 'process_queue_manager'):
                all_tasks = self.app.process_queue_manager.get_all_tasks()

                # Filter completed tasks from task list
                completed_tasks = []
                for task in all_tasks:
                    # Convert enum to string for comparison if needed
                    status = task["status"]
                    if hasattr(status, "name"):  # It's an enum
                        status = status.name

                    if status in ["COMPLETED", "FAILED", "CANCELLED"]:
                        completed_tasks.append(task)

                self.clear_btn["state"] = "normal" if completed_tasks else "disabled"
            else:
                self.clear_btn["state"] = "disabled"
        except Exception as e:
            print(f"Error updating clear button: {e}")
            self.clear_btn["state"] = "disabled"

    def _on_clear_completed(self):
        """Handle click on the clear completed button."""
        try:
            if hasattr(self.app, 'process_queue_manager'):
                cleared = self.app.process_queue_manager.clear_completed_tasks()
                if cleared > 0:
                    self.app.status_var.set(f"Cleared {cleared} completed processes from queue")
                else:
                    self.app.status_var.set("No completed processes to clear")

                # Update the display after clearing
                self.update_display()
        except Exception as e:
            print(f"Error clearing completed tasks: {e}")
            self.app.status_var.set(f"Error clearing completed tasks: {str(e)}")