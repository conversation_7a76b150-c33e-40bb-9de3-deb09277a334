"""
Utilities for managing external processes for the Tire Panorama Tool.
"""
import os
import time
import traceback
import subprocess
import sys
import logging
from utils.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>

def log_debug_path(message):
    ErrorHandler.log_debug(f"PATH_LOG: {message}")

def build_extract_frames(base_dir, status_callback=None):
    """
    Try to automatically compile extract_frames using the centralized build structure

    Args:
        base_dir: Base directory where source files are located
        status_callback: Function to call with status updates

    Returns:
        Path to compiled executable if successful, None otherwise
    """
    log_debug_path(f"build_extract_frames - Initial base_dir: {base_dir}")
    try:
        log_debug_path(f"build_extract_frames - Base directory (resolved): {base_dir}")
        ErrorHandler.log_debug(f"Base directory: {base_dir}")

        # Get the TireStitcher directory
        tire_stitcher_dir = os.path.dirname(base_dir)
        log_debug_path(f"build_extract_frames - tire_stitcher_dir: {tire_stitcher_dir}")

        # Get the ReifenScanner directory (parent of TireStitcher)
        reifen_scanner_dir = os.path.dirname(tire_stitcher_dir)
        log_debug_path(f"build_extract_frames - reifen_scanner_dir: {reifen_scanner_dir}")

        # Build directory is centralized in the ReifenScanner folder
        build_dir = os.path.join(reifen_scanner_dir, "build")
        log_debug_path(f"build_extract_frames - build_dir: {build_dir}")

        # Show progress in the GUI
        if status_callback:
            status_callback("Using centralized build structure with MinGW")

        # Create build directory if it doesn't exist
        os.makedirs(build_dir, exist_ok=True)
        log_debug_path(f"build_extract_frames - Ensured build_dir exists: {build_dir}")

        # Change to build directory
        original_dir = os.getcwd()
        log_debug_path(f"build_extract_frames - Original CWD: {original_dir}")
        os.chdir(build_dir)
        log_debug_path(f"build_extract_frames - Changed CWD to build_dir: {build_dir}")

        try:
            # Run CMake with MinGW generator
            if status_callback:
                status_callback("Running CMake with MinGW generator...")
            log_debug_path(f"build_extract_frames - Running CMake command: cmake .. -G \"MinGW Makefiles\" in {os.getcwd()}")

            cmake_process = subprocess.run(
                ["cmake", "..", "-G", "MinGW Makefiles"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            ErrorHandler.log_debug(f"CMake output:\n{cmake_process.stdout}")

            # Build extract_frames using CMake's build command with MinGW
            if status_callback:
                status_callback("Building extract_frames with MinGW...")
            log_debug_path(f"build_extract_frames - Running CMake build command for extract_frames: cmake --build . --target extract_frames in {os.getcwd()}")

            build_process = subprocess.run(
                ["cmake", "--build", ".", "--target", "extract_frames"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            ErrorHandler.log_debug(f"Build output:\n{build_process.stdout}")

            # Log the output from the C++ program
            if cmake_process.stdout:
                for line in cmake_process.stdout.splitlines():
                    ErrorHandler.log_debug(f"[extract_frames.exe] {line}")
            if cmake_process.stderr:
                for line in cmake_process.stderr.splitlines():
                    ErrorHandler.log_error(f"[extract_frames.exe] {line}")

            # The executable should be in the ReifenScanner folder
            exe_ext = ".exe"
            extract_frames_exe = os.path.join(reifen_scanner_dir, "extract_frames" + exe_ext)
            log_debug_path(f"build_extract_frames - Expected extract_frames_exe path: {extract_frames_exe}")

            # Check if the executable exists
            if os.path.exists(extract_frames_exe) and os.access(extract_frames_exe, os.X_OK):
                log_debug_path(f"build_extract_frames - extract_frames_exe found at: {extract_frames_exe}")
                if status_callback:
                    status_callback(f"extract_frames successfully built at: {extract_frames_exe}")
                return extract_frames_exe
            else:
                # If not found, build stitch_tire as well
                if status_callback:
                    status_callback("Building stitch_tire with MinGW...")
                log_debug_path(f"build_extract_frames - extract_frames_exe not found, attempting to build stitch_tire. Running CMake build command for stitch_tire: cmake --build . --target stitch_tire in {os.getcwd()}")

                build_process = subprocess.run(
                    ["cmake", "--build", ".", "--target", "stitch_tire"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=True
                )
                ErrorHandler.log_debug(f"Build output for stitch_tire:\n{build_process.stdout}")

                # Check again for extract_frames
                if os.path.exists(extract_frames_exe) and os.access(extract_frames_exe, os.X_OK):
                    log_debug_path(f"build_extract_frames - extract_frames_exe found after building stitch_tire: {extract_frames_exe}")
                    if status_callback:
                        status_callback(f"extract_frames successfully built at: {extract_frames_exe}")
                    return extract_frames_exe
                else:
                    log_debug_path(f"build_extract_frames - extract_frames_exe still not found after building stitch_tire. Expected at: {extract_frames_exe}")
                    raise Exception("extract_frames was compiled but executable not found in ReifenScanner folder")

        finally:
            # Change back to original directory
            os.chdir(original_dir)
            log_debug_path(f"build_extract_frames - Restored CWD to: {original_dir}")

    except Exception as e:
        log_debug_path(f"build_extract_frames - Exception occurred: {e}")
        error_message = f"Error building extract_frames: {str(e)}"
        ErrorHandler.log_error(error_message)
        traceback.print_exc()
        if status_callback:
            status_callback(error_message)
        return None

def run_panorama_stitching(cpp_executable, input_dir, output_dir, file_pattern,
                          strip_width=None, max_frames=None, enable_blending=True,
                          start_frame=0, status_callback=None, progress_callback=None, process_cancelled_check=None,
                          frame_callback=None, serial_number=None, subproject_type=None,
                          resolution_mode=None): # Added resolution_mode
    """
    Run the panorama stitching process.

    Args:
        cpp_executable: Path to the stitching executable
        input_dir: Directory containing input frames
        output_dir: Directory to save panorama output
        file_pattern: Pattern to match input files
        strip_width: Width of strips to use (optional)
        max_frames: Maximum number of frames to process (optional)
        enable_blending: Whether to enable blending
        start_frame: Frame index to start from (for resuming)
        status_callback: Function to call with status updates
        progress_callback: Function to call with progress updates (0-100)
        process_cancelled_check: Function that returns True if process should be cancelled
        frame_callback: Function to call with current frame index updates
        serial_number: Serial number for unique filenames
        subproject_type: Subproject type (e.g., frontal, left, right)
        resolution_mode: Resolution mode ("4K" or "8K")

    Returns:
        True if successful, False otherwise
    """
    try:
        # Convert relative to absolute paths for better reliability
        input_dir_abs = os.path.abspath(input_dir)
        output_dir_abs = os.path.abspath(output_dir)

        # Debug information about directory contents
        ErrorHandler.log_debug(f"Contents of input directory ({input_dir_abs}):")
        try:
            for ext in ['.JPG', '.jpg', '.JPEG', '.jpeg', '.PNG', '.png']:
                matching_files = [f for f in os.listdir(input_dir_abs) if f.endswith(ext)]
                if matching_files:
                    ErrorHandler.log_debug(f"  {len(matching_files)} files with extension {ext}")
                    ErrorHandler.log_debug(f"  Example files: {matching_files[:3]}")
        except Exception as e:
            ErrorHandler.log_error(f"Error listing directory: {e}")

        # Check if the executable exists
        if cpp_executable is None or not os.path.exists(cpp_executable):
            # Attempt to find the executable based on the execution environment
            if getattr(sys, 'frozen', False):
                # Packaged application: look in the _internal directory
                base_path = sys._MEIPASS
            else:
                # Unpackaged application: look in the ReifenScanner directory
                script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                base_path = os.path.dirname(script_dir)

            expected_exe_path = os.path.join(base_path, "stitch_tire.exe")

            if os.path.exists(expected_exe_path):
                cpp_executable = expected_exe_path
                ErrorHandler.log_debug(f"Found executable at: {cpp_executable}")
            else:
                # Fallback for packaged app: check one level up from _internal, common for some PyInstaller setups
                if getattr(sys, 'frozen', False):
                    alt_base_path = os.path.dirname(sys._MEIPASS) # one level up from _internal
                    alt_expected_exe_path = os.path.join(alt_base_path, "stitch_tire.exe")
                    if os.path.exists(alt_expected_exe_path):
                        cpp_executable = alt_expected_exe_path
                        ErrorHandler.log_debug(f"Found executable at alternative packaged location: {cpp_executable}")
                    else:
                         raise Exception(f"Stitching executable not found. Looked in {expected_exe_path} and {alt_expected_exe_path}. Please ensure stitch_tire.exe is available.")
                else:
                    raise Exception(f"Stitching executable not found. Looked in {expected_exe_path}. Please ensure stitch_tire.exe is available.")

        # Create command with parameters for the C++ program
        cmd = [
            cpp_executable,
            "--input", input_dir_abs,
            "--output", output_dir_abs,
            "--file-pattern", file_pattern
        ]

        # Add serial number and subproject type if provided
        if serial_number:
            cmd.extend(["--serial-number", serial_number])
        if subproject_type:
            cmd.extend(["--subproject-type", subproject_type])

        # Add resolution mode if specified
        if resolution_mode:
            cmd.extend(["--resolution-mode", resolution_mode])

        # Add strip width if specified
        if strip_width is not None and strip_width > 0:
            cmd.extend(["--strip-width", str(strip_width)])

        # Add max frames if specified
        if max_frames is not None and max_frames > 0:
            cmd.extend(["--max-frames", str(max_frames)])

        # Add blending flag - ensure this flag is working
        if not enable_blending:
            cmd.append("--no-blending")

        # Log command for debugging
        ErrorHandler.log_debug(f"Running stitching command: {' '.join(cmd)}")

        # Start stitching process with environment variables to ensure proper resource allocation
        env = os.environ.copy()

        # Set environment variables to control OpenMP behavior
        # Limit the number of threads to avoid resource contention
        env['OMP_NUM_THREADS'] = '4'  # Limit to 4 threads per process
        env['OMP_THREAD_LIMIT'] = '4'
        env['OMP_DYNAMIC'] = 'FALSE'  # Disable dynamic adjustment

        # Add DLL directory to PATH for the process
        # Get the ReifenScanner directory (parent of TireStitcher)
        tire_stitcher_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        reifen_scanner_dir = os.path.dirname(tire_stitcher_dir)
        dll_dir = os.path.join(reifen_scanner_dir, "dll")

        # Add DLL directory to PATH if it exists
        if os.path.exists(dll_dir):
            ErrorHandler.log_debug(f"Adding DLL directory to PATH: {dll_dir}")
            if 'PATH' in env:
                env['PATH'] = dll_dir + os.pathsep + env['PATH']
            else:
                env['PATH'] = dll_dir

        # Start the process with the modified environment
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,  # Line buffered
            env=env,
            creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0  # Add this line
        )

        # Real-time monitoring of output for progress and ETA
        start_time = time.time()
        current_frame = 0
        total_frames = 0
        last_progress_percent = 0

        # Get total frame count for progress calculation
        try:
            frame_files = [f for f in os.listdir(input_dir_abs) if f.endswith(('.JPG', '.jpg', '.JPEG', '.jpeg'))]
            total_frames = len(frame_files)
            ErrorHandler.log_debug(f"Found {total_frames} frames for stitching progress tracking")
        except Exception as e:
            ErrorHandler.log_warning(f"Could not count frames for progress tracking: {e}")
            total_frames = 0

        # Capture and log output/error from the C++ program
        for line in process.stdout:
            line = line.strip()
            if line:
                ErrorHandler.log_debug(f"[stitch_tire.exe] {line}")
                if status_callback:
                    status_callback(line)

                # Track frame progress for progress callback
                if frame_callback and "Current frame" in line:
                    try:
                        frame_num = int(line.split(":")[1].strip().split("/")[0])
                        frame_callback(frame_num)
                        current_frame = frame_num

                        # Calculate and report progress
                        if total_frames > 0 and progress_callback:
                            progress_percent = min(int((current_frame / total_frames) * 100), 100)
                            # Only update if progress has changed by at least 1% to avoid excessive updates
                            if progress_percent > last_progress_percent:
                                progress_callback(progress_percent)
                                last_progress_percent = progress_percent

                    except (IndexError, ValueError) as e:
                        ErrorHandler.log_warning(f"Could not parse frame number from stitch_tire.exe output: {line} - {e}")

                # Look for other progress indicators in the C++ output
                elif progress_callback:
                    # Check for percentage indicators in the output
                    if "%" in line and any(keyword in line.lower() for keyword in ["progress", "complete", "done"]):
                        try:
                            # Extract percentage from line
                            import re
                            percent_match = re.search(r'(\d+)%', line)
                            if percent_match:
                                progress_percent = int(percent_match.group(1))
                                if progress_percent > last_progress_percent:
                                    progress_callback(progress_percent)
                                    last_progress_percent = progress_percent
                        except (ValueError, AttributeError):
                            pass

                    # Check for completion indicators
                    elif any(keyword in line.lower() for keyword in ["saving final", "panorama complete", "stitching complete"]):
                        progress_callback(100)
                        last_progress_percent = 100

        process.wait()

        # Log final error if process failed
        if process.returncode != 0:
            error_output = process.stderr.read().strip()
            if error_output:
                ErrorHandler.log_error(f"[stitch_tire.exe] Error: {error_output}")
            raise Exception(f"Panorama stitching failed with exit code {process.returncode}. Check log file.")

        # Check for panorama files in different formats
        panorama_found = False

        # First check for standard panorama in different formats
        # Look for files with pattern: tire_*_full.* or tire_unwrapped_full.*
        for file in os.listdir(output_dir):
            if (file.startswith("tire_") and "_full." in file) or file.startswith("tire_unwrapped_full."):
                panorama_found = True
                break

        # If not found, check for enhanced version
        if not panorama_found:
            for file in os.listdir(output_dir):
                if (file.startswith("tire_") and "_enhanced." in file) or file.startswith("tire_unwrapped_enhanced."):
                    panorama_found = True
                    break

        # If still not found, check for section files
        if not panorama_found:
            try:
                for file in os.listdir(output_dir):
                    if file.startswith("tire_unwrapped_section_"):
                        panorama_found = True
                        if status_callback:
                            status_callback("Found section-based panorama")
                        break
            except Exception:
                pass

        # If any panorama was found, return success
        if panorama_found:
            # Ensure progress reaches 100% on successful completion
            if progress_callback and last_progress_percent < 100:
                progress_callback(100)
            return True

        # If we get here, no panorama was found, but based on the console output,
        # the process appears to have completed successfully with sections
        # Return success anyway
        if status_callback:
            status_callback("No standard panorama found, but processing appears successful")

        # Ensure progress reaches 100% even if no standard panorama found
        if progress_callback and last_progress_percent < 100:
            progress_callback(100)
        return True

    except Exception as e:
        error_message = str(e)
        error_traceback = traceback.format_exc()
        ErrorHandler.log_error(f"Error: {error_message}\n{error_traceback}")

        if status_callback:
            status_callback(f"Error: {error_message}")

        return False

def terminate_process(process, timeout=0.5):
    """
    Safely terminate a subprocess and all its children.

    Args:
        process: The subprocess.Popen object
        timeout: Time to wait before forcing termination

    Returns:
        True if termination was successful, False otherwise
    """
    if not process or process.poll() is not None:
        # Process doesn't exist or already terminated
        return True

    try:
        # Get process ID for more aggressive termination if needed
        pid = process.pid
        ErrorHandler.log_debug(f"Terminating process with PID {pid}")

        # First try graceful termination
        process.terminate()

        # Wait briefly for process to terminate
        for _ in range(5):  # Check multiple times with short intervals
            if process.poll() is not None:
                ErrorHandler.log_debug(f"Process {pid} terminated gracefully")
                return True
            time.sleep(timeout/5)

        # If still running, force kill
        if process.poll() is None:
            ErrorHandler.log_debug(f"Process {pid} did not terminate gracefully, forcing kill")
            process.kill()

            # Wait again briefly
            time.sleep(timeout)

            # If still running, use OS-specific commands to kill the process tree
            if process.poll() is None:
                ErrorHandler.log_debug(f"Process {pid} still running after kill, using OS-specific commands")

                # Windows process termination
                try:
                    # Use taskkill to forcefully terminate the process and its children
                    subprocess.run(
                        ['taskkill', '/F', '/T', '/PID', str(pid)],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        check=False
                    )
                except Exception:
                    ErrorHandler.log_error(f"Error using Windows taskkill for PID {pid}")

        # Final check
        if process.poll() is None:
            ErrorHandler.log_warning(f"WARNING: Process {pid} could not be terminated!")
            return False

        ErrorHandler.log_debug(f"Process {pid} successfully terminated")
        return True

    except Exception as e:
        ErrorHandler.log_error(f"Error terminating process: {e}")
        traceback.print_exc()
        return False