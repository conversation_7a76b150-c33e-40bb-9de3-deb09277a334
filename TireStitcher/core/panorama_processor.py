"""
Core logic for processing panoramas in the Tire Panorama Tool.
"""
import os
import threading
import traceback
import json
from datetime import datetime

from core.file_utils import create_project_directory, copy_images_to_project, find_executables
from core.image_utils import extract_frames_opencv, extract_frames_using_external
from core.process_utils import run_panorama_stitching, build_extract_frames, terminate_process
from utils.error_handler import error_handler

class PanoramaProcessor:
    """
    Class for handling panorama processing operations.
    """
    def __init__(self, gui_dir, input_base_dir, projects_dir):
        """
        Initialize the panorama processor.

        Args:
            gui_dir: Directory containing the GUI files
            input_base_dir: Base directory for input files
            projects_dir: Directory for project storage
        """
        self.gui_dir = gui_dir
        self.input_base_dir = input_base_dir
        self.projects_dir = projects_dir

        # Find necessary executables
        self.cpp_executable = self.find_cpp_executable()
        self.extract_frames_executable = self.find_extract_frames_executable()

        # For process management - use a lock to protect access to process state
        self.process_lock = threading.RLock()
        self.current_process = None
        self.process_cancelled = False

        # Add a thread-local storage for process state to ensure isolation between tasks
        self.thread_local = threading.local()

        # List of panoramas
        self.panoramas = []
        self.current_panorama_index = -1

    def find_cpp_executable(self):
        """Find the C++ stitching executable in the TireStitcher folder"""
        return find_executables(self.gui_dir, "stitch_tire")

    def find_extract_frames_executable(self):
        """Find the extract_frames executable in the TireStitcher folder"""
        return find_executables(self.gui_dir, "extract_frames")

    def is_process_cancelled(self):
        """Check if the current process has been cancelled"""
        # First check thread-local storage for task-specific cancellation
        if hasattr(self.thread_local, 'process_cancelled'):
            return self.thread_local.process_cancelled
        # Fall back to global cancellation flag
        with self.process_lock:
            return self.process_cancelled

    def cancel_current_process(self):
        """Cancel the current process and ensure all related processes are terminated"""
        with self.process_lock:
            # Log instead of print
            error_handler.log_error("Cancelling current process...")
            self.process_cancelled = True

            # Set thread-local cancellation flag if it exists
            if hasattr(self.thread_local, 'process_cancelled'):
                self.thread_local.process_cancelled = True
                error_handler.log_error("Set thread-local cancellation flag")

            # Terminate any running process
            if self.current_process and self.current_process.poll() is None:
                error_handler.log_error(f"Terminating process with PID {self.current_process.pid}")
                terminate_success = terminate_process(self.current_process)

                # If termination failed or as an additional measure, try more aggressive methods
                if not terminate_success:
                    error_handler.log_error("Initial termination failed, trying more aggressive methods")
                    try:
                        # Try to kill the process group
                        import os
                        import signal
                        error_handler.log_error(f"Sending SIGKILL to process group {os.getpgid(self.current_process.pid)}")
                        os.killpg(os.getpgid(self.current_process.pid), signal.SIGKILL)
                    except Exception as e:
                        error_handler.log_error(f"Error killing process group: {e}")

                    # As a last resort, use system commands
                    try:
                        import subprocess
                        error_handler.log_error(f"Using system commands to kill process {self.current_process.pid} and children")
                        subprocess.call(['pkill', '-P', str(self.current_process.pid)])
                        subprocess.call(['kill', '-9', str(self.current_process.pid)])
                    except Exception as e:
                        error_handler.log_error(f"Error using system kill commands: {e}")
            else:
                error_handler.log_error("No current process to terminate")

    def reset_cancellation(self):
        """Reset the cancellation flag"""
        # Reset both global and thread-local cancellation flags
        with self.process_lock:
            self.process_cancelled = False
        # Initialize thread-local storage for this task
        self.thread_local.process_cancelled = False

    def process_panorama(self, project_name, project_dir, output_dir, input_path, input_type,
                    rotate_frames=True, strip_width=None, num_samples=None, max_frames=None,
                    enable_blending=True, status_callback=None, progress_callback=None,
                    continue_from_state=None):
        """
        Process a panorama from video or images.

        Args:
            project_name: Name of the project
            project_dir: Directory for the project
            output_dir: Directory for panorama output
            input_path: Path to the input video or image folder
            input_type: Type of input ('video' or 'folder')
            rotate_frames: Whether to rotate frames 90 degrees
            strip_width: Width of strips for stitching
            num_samples: Number of frames to sample
            max_frames: Maximum frames to process
            enable_blending: Whether to enable blending
            status_callback: Function to call with status updates
            progress_callback: Function to call with progress updates
            continue_from_state: Optional state to continue from

        Returns:
            Path to panorama if successful, None otherwise
        """

        # Initialize phase tracking for two-phase progress
        self.current_phase = 1  # 1 = extraction, 2 = stitching
        self.total_phases = 2

        # Create a two-phase progress callback wrapper
        def two_phase_progress_callback(phase_progress):
            """
            Convert single-phase progress (0-100) to two-phase progress

            Args:
                phase_progress: Progress within current phase (0-100)
            """
            if progress_callback:
                if self.current_phase == 1:
                    # Phase 1: Frame extraction (0-50% of total)
                    total_progress = phase_progress * 0.5
                elif self.current_phase == 2:
                    # Phase 2: Stitching (50-100% of total)
                    total_progress = 50 + (phase_progress * 0.5)
                else:
                    total_progress = phase_progress

                # Ensure progress is within bounds
                final_progress = min(int(total_progress), 100)
                progress_callback(final_progress)

        # Create a two-phase status callback wrapper
        def two_phase_status_callback(message, current=None, total=None, eta=None, frame_rate=None, progress_percent=None, correlation=None):
            """
            Wrap status callback to include phase information
            """
            if status_callback:
                # Add phase indicator to message if it doesn't already contain it
                if message and not any(phase_indicator in message for phase_indicator in ["1/2", "2/2"]):
                    phase_message = f"Phase {self.current_phase}/2: {message}"
                else:
                    phase_message = message

                # Call original status callback with phase information
                status_callback(phase_message, current, total, eta, frame_rate, progress_percent, correlation)

        try:
            # Initialize variables that need to be accessible in all paths
            input_dir = None
            file_pattern = "IMG_*.JPG"  # Default pattern for image files

            # Initialize logger for this panorama generation
            # Determine the subproject type from the project directory structure
            # The input_type is 'video' or 'folder', not the subproject type we need
            subproject_type = "unknown"
            if "/frontal/" in project_dir:
                subproject_type = "frontal"
            elif "/left/" in project_dir:
                subproject_type = "left"
            elif "/right/" in project_dir:
                subproject_type = "right"

            # Initialize the logger with the correct subproject type
            # The error_handler will ensure the output directory exists
            error_handler.initialize_logger(output_dir, project_name, subproject_type)

            # Log the start of panorama generation
            error_handler.log_error(f"Starting panorama generation for {project_name} {subproject_type}")

            # Reset cancellation flag
            self.reset_cancellation()

            # State to save in case of cancellation
            processing_state = {
                "project_name": project_name,
                "project_dir": project_dir,
                "input_path": input_path,
                "input_type": input_type,
                "parameters": {
                    "rotate_frames": rotate_frames,
                    "strip_width": strip_width,
                    "num_samples": num_samples,
                    "max_frames": max_frames,
                    "enable_blending": enable_blending
                },
                "file_pattern": file_pattern  # Store the file pattern in state
            }

            # Check if continuing from previous state
            if continue_from_state:
                # Restore state variables
                if status_callback:
                    status_callback("Continuing from previous state...")

                # Extract state information
                frames_dir = continue_from_state.get("frames_dir")
                last_processed_frame = continue_from_state.get("last_processed_frame", 0)
                processing_stage = continue_from_state.get("processing_stage", "frame_extraction")

                # Restore file pattern if available
                if "file_pattern" in continue_from_state:
                    file_pattern = continue_from_state["file_pattern"]

                if frames_dir and os.path.exists(frames_dir):
                    input_dir = frames_dir
                    processing_state["frames_dir"] = frames_dir
                    processing_state["last_processed_frame"] = last_processed_frame
                    processing_state["processing_stage"] = processing_stage

                    if status_callback:
                        stage_text = "frame extraction" if processing_stage == "frame_extraction" else "panorama stitching"
                        status_callback(f"Continuing from {stage_text} at frame {last_processed_frame}...")

                    # If we were in the frame extraction stage, we need to continue from there
                    # instead of skipping to stitching
                    if processing_stage == "frame_extraction" and input_type == "video":
                        # We need to continue frame extraction
                        input_dir = None  # Force the code to re-enter the frame extraction path

            # Prepare input images if not continuing or if continuation frames are missing
            if input_dir is None:
                if input_type == "video":
                    # Extract frames from video
                    frames_dir = os.path.join(project_dir, "frames")
                    os.makedirs(frames_dir, exist_ok=True)

                    if status_callback:
                        status_callback("Preparing frame extraction with FFmpeg...")

                    # Import the FFmpeg extraction function
                    from core.image_utils import extract_frames_ffmpeg

                    # Determine frame step from num_samples if provided
                    frame_step = 1
                    if num_samples and num_samples.isdigit():
                        frame_step = max(1, int(1 / float(num_samples)))

                    # Save initial state before starting extraction
                    processing_state["processing_stage"] = "frame_extraction"
                    processing_state["frames_dir"] = frames_dir
                    processing_state["file_pattern"] = file_pattern
                    state_path = os.path.join(project_dir, "processing_state.json")
                    with open(state_path, 'w') as f:
                        json.dump(processing_state, f)

                    # Get starting frame if continuing from previous extraction
                    start_frame = 0
                    if continue_from_state and processing_stage == "frame_extraction":
                        start_frame = continue_from_state.get("last_processed_frame", 0)
                        if status_callback and start_frame > 0:
                            status_callback(f"Continuing frame extraction from frame {start_frame}")

                    # Create a frame callback for extraction to track progress
                    def extraction_frame_callback(frame):
                        # Update the frame state in the project directory
                        self._update_frame_state(project_dir, frame)

                    # Import the FFmpeg extraction function with process reference
                    from core.image_utils import extract_frames_ffmpeg

                    # Use FFmpeg for extraction and store the process reference
                    # so it can be terminated if needed
                    def store_process_ref(process):
                        # Store the process reference for cancellation
                        with self.process_lock:
                            self.current_process = process
                        return process

                    # Use FFmpeg for extraction with two-phase progress tracking
                    success = extract_frames_ffmpeg(
                        input_path,
                        frames_dir,
                        frame_step=frame_step,
                        samples=num_samples,
                        max_frames=max_frames,
                        rotate=rotate_frames,
                        status_callback=two_phase_status_callback,
                        progress_callback=two_phase_progress_callback,
                        process_cancelled_check=self.is_process_cancelled,
                        start_frame=start_frame,
                        frame_callback=extraction_frame_callback,
                        process_ref_callback=store_process_ref
                    )

                    # Check if extraction was successful or cancelled
                    if not success:
                        if self.is_process_cancelled():
                            # State already saved before starting extraction
                            if status_callback:
                                status_callback("Frame extraction cancelled. State saved for continuation.")
                            return None
                        else:
                            raise Exception("Frame extraction failed. Check logs.")

                    # Transition to phase 2 (stitching)
                    self.current_phase = 2

                    # Use frames directory for stitching
                    input_dir = frames_dir

                    # Update state for stitching phase
                    processing_state["processing_stage"] = "stitching"
                    processing_state["last_processed_frame"] = 0

                    # Save state before starting stitching
                    with open(state_path, 'w') as f:
                        json.dump(processing_state, f)

                    # Verify files exist
                    file_list = os.listdir(frames_dir)
                    if not file_list:
                        raise Exception(f"No files found in {frames_dir} after extraction")

                    if status_callback:
                        two_phase_status_callback(f"Frames extracted, proceeding with stitching...")

                elif input_type == "folder":
                    # For image folder input
                    input_dir = os.path.join(project_dir, "images")
                    os.makedirs(input_dir, exist_ok=True)

                    if status_callback:
                        status_callback("Copying images...")

                    # Save initial state before starting copying
                    processing_state["processing_stage"] = "frame_extraction"
                    processing_state["frames_dir"] = input_dir
                    processing_state["file_pattern"] = file_pattern
                    state_path = os.path.join(project_dir, "processing_state.json")
                    with open(state_path, 'w') as f:
                        json.dump(processing_state, f)

                    # Copy images to project directory
                    image_count = 0
                    for count, total, percent in copy_images_to_project(input_path, input_dir):
                        image_count = count
                        # Use two-phase progress callback for image copying
                        two_phase_progress_callback(percent)
                        if status_callback and count % 5 == 0:
                            two_phase_status_callback(f"Copied {count} of {total} images...")

                        # Check for cancellation
                        if self.is_process_cancelled():
                            # State already saved before starting
                            if status_callback:
                                two_phase_status_callback("Image copying cancelled. State saved for continuation.")
                            return None

                    if image_count == 0:
                        raise Exception("No image files found in the selected folder")

                    # Transition to phase 2 (stitching)
                    self.current_phase = 2

                    # Update state for stitching phase
                    processing_state["processing_stage"] = "stitching"
                    processing_state["last_processed_frame"] = 0

                    # Save state before starting stitching
                    with open(state_path, 'w') as f:
                        json.dump(processing_state, f)

                else:
                    # Handle unknown input type
                    raise Exception(f"Unknown input type: {input_type}. Expected 'video' or 'folder'.")

                # Store input directory in processing state
                processing_state["frames_dir"] = input_dir

            # Make sure input_dir is set
            if not input_dir:
                raise Exception("Failed to set input directory. Check logs for details.")

            # Check if process was already cancelled
            if self.is_process_cancelled():
                # State was already saved in previous steps
                return None

            # Ensure we're in phase 2 for stitching (in case we're continuing from saved state)
            if continue_from_state and continue_from_state.get("processing_stage") == "stitching":
                self.current_phase = 2

            # Run stitching using C++ executable
            two_phase_status_callback("Generating panorama with C++ engine...")

            # Get starting frame index if continuing
            start_frame = 0
            if continue_from_state and "last_processed_frame" in continue_from_state:
                start_frame = continue_from_state["last_processed_frame"]
                two_phase_status_callback(f"Continuing from frame {start_frame}...")

            # Update processing state for stitching
            if "processing_stage" not in processing_state or processing_state["processing_stage"] != "stitching":
                processing_state["processing_stage"] = "stitching"
                processing_state["last_processed_frame"] = start_frame
                processing_state["file_pattern"] = file_pattern
                state_path = os.path.join(project_dir, "processing_state.json")
                with open(state_path, 'w') as f:
                    json.dump(processing_state, f)

            # Store the current process in thread-local storage to ensure isolation
            # between multiple tasks
            def thread_safe_frame_callback(frame):
                # Use thread-local storage to track the current frame for this specific task
                self.thread_local.current_frame = frame
                # Update the frame state in the project directory
                self._update_frame_state(project_dir, frame)

            # Run the stitching process with two-phase progress tracking
            success = run_panorama_stitching(
                self.cpp_executable,
                input_dir,
                output_dir,
                file_pattern,
                strip_width=strip_width,
                max_frames=max_frames,
                enable_blending=enable_blending,
                start_frame=start_frame,  # Pass start_frame parameter
                status_callback=two_phase_status_callback,
                progress_callback=two_phase_progress_callback,
                process_cancelled_check=self.is_process_cancelled,
                frame_callback=thread_safe_frame_callback,
                serial_number=project_name,  # Pass serial number for unique filenames
                subproject_type=os.path.basename(os.path.dirname(output_dir))  # Extract subproject type from path
            )

            # Check for success or cancellation
            if not success:
                if self.is_process_cancelled():
                    # State updates are handled by frame_callback during stitching
                    two_phase_status_callback("Stitching process cancelled. State saved for continuation.")
                    return None
                else:
                    # Save state in case of error too
                    processing_state["failed"] = True
                    state_path = os.path.join(project_dir, "processing_state.json")
                    with open(state_path, 'w') as f:
                        json.dump(processing_state, f)

                    raise Exception("Stitching failed. Check logs, but you can try continuing later.")

            # Check for panorama files in different formats
            panorama_found = False
            result_path = None

            # First check for standard panorama in different formats
            # Look for files with pattern: tire_*_full.* or tire_unwrapped_full.*
            for file in os.listdir(output_dir):
                if (file.startswith("tire_") and "_full." in file):
                    path = os.path.join(output_dir, file)
                    panorama_found = True
                    result_path = path
                    break

            # If not found, check for enhanced version
            if not panorama_found:
                for file in os.listdir(output_dir):
                    if (file.startswith("tire_") and "_enhanced." in file):
                        path = os.path.join(output_dir, file)
                        panorama_found = True
                        result_path = path
                        break

            # If still not found, check for section files
            if not panorama_found:
                try:
                    section_files = [f for f in os.listdir(output_dir)
                                   if ("_section_" in f) and
                                   any(f.endswith(ext) for ext in [".png", ".jpg", ".JPG"])]
                    if section_files:
                        panorama_found = True
                        result_path = os.path.join(output_dir, section_files[0])
                        if status_callback:
                            status_callback("Found section-based panorama")
                except Exception:
                    pass

            # If we found any valid result, return it
            if panorama_found:
                # Clean up saved state if successful
                state_path = os.path.join(project_dir, "processing_state.json")
                if os.path.exists(state_path):
                    os.remove(state_path)

                # Final status update
                two_phase_status_callback("Panorama successfully created!")
                # Set progress to 100% for completion
                two_phase_progress_callback(100)

                # If we don't have a specific path but panorama was found, use a placeholder
                if not result_path:
                    result_path = os.path.join(output_dir, "tire_unwrapped_sections_exist")

                return result_path

            # If we get here, no panorama was found
            # Save state in case of error
            processing_state["failed"] = True
            state_path = os.path.join(project_dir, "processing_state.json")
            with open(state_path, 'w') as f:
                json.dump(processing_state, f)

            raise Exception("Stitching completed, but no panorama found")

        except Exception as e:
            error_message = str(e)
            error_traceback = traceback.format_exc()
            # Log the error instead of printing to console
            error_handler.log_error(f"Error: {error_message}")
            error_handler.log_error(error_traceback)

            two_phase_status_callback(f"Error: {error_message}")

            # Try to save state even on exception
            try:
                if 'processing_state' in locals():
                    processing_state["error"] = error_message
                    state_path = os.path.join(project_dir, "processing_state.json")
                    with open(state_path, 'w') as f:
                        json.dump(processing_state, f)
            except:
                # If we fail to save state, just continue
                pass

            return None

        finally:
            # Clean up
            self.current_process = None

    def _update_frame_state(self, project_dir, frame):
        """
        Update the processing state with the current frame.

        Args:
            project_dir: Project directory
            frame: Current frame being processed
        """
        try:
            state_path = os.path.join(project_dir, "processing_state.json")
            if os.path.exists(state_path):
                # Read current state
                with open(state_path, 'r') as f:
                    state_data = json.load(f)

                # Update frame number
                state_data["last_processed_frame"] = frame

                # Ensure file_pattern is present
                if "file_pattern" not in state_data:
                    state_data["file_pattern"] = "IMG_*.JPG"

                # Write updated state
                with open(state_path, 'w') as f:
                    json.dump(state_data, f)
        except Exception as e:
            print(f"Error updating frame state: {e}")

    def start_panorama_process_thread(self, project_info, input_info, params,
                                     status_callback=None, progress_callback=None,
                                     completion_callback=None, continue_from_state=None):
        """
        Start panorama processing in a separate thread.

        Args:
            project_info: Dictionary with project name and directory
            input_info: Dictionary with input path and type
            params: Dictionary with processing parameters
            status_callback: Function to call with status updates
            progress_callback: Function to call with progress updates
            completion_callback: Function to call when processing is complete
            continue_from_state: Optional state to continue from
        """
        # Create processing thread
        processing_thread = threading.Thread(
            target=self._process_panorama_thread,
            args=(project_info, input_info, params, status_callback,
                 progress_callback, completion_callback, continue_from_state),
            daemon=True
        )
        processing_thread.start()

    def _process_panorama_thread(self, project_info, input_info, params,
                               status_callback, progress_callback, completion_callback,
                               continue_from_state=None):
        """
        Thread function for panorama processing.

        Args:
            project_info: Dictionary with project name and directory
            input_info: Dictionary with input path and type
            params: Dictionary with processing parameters
            status_callback: Function to call with status updates
            progress_callback: Function to call with progress updates
            completion_callback: Function to call when processing is complete
            continue_from_state: Optional state to continue from
        """
        # Extract info from dictionaries
        project_name = project_info.get('name')
        project_dir = project_info.get('dir')
        output_dir = os.path.join(project_dir, "output")

        input_path = input_info.get('path')
        input_type = input_info.get('type')

        # Extract parameters
        rotate_frames = params.get('rotate_frames', True)
        strip_width = params.get('strip_width')
        num_samples = params.get('num_samples')
        max_frames = params.get('max_frames')
        enable_blending = params.get('enable_blending', True)

        # Process the panorama
        result = self.process_panorama(
            project_name, project_dir, output_dir, input_path, input_type,
            rotate_frames, strip_width, num_samples, max_frames, enable_blending,
            status_callback, progress_callback, continue_from_state
        )

        # Call completion callback
        if completion_callback:
            completion_callback(result)

    def auto_build_extract_frames(self, status_callback=None):
        """
        Attempt to automatically build the extract_frames executable.

        Args:
            status_callback: Function to call with status updates

        Returns:
            Path to the built executable if successful, None otherwise
        """
        return build_extract_frames(self.gui_dir, status_callback)